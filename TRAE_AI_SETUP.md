# Setting Up Supabase MCP Server for Trae AI

This guide will help you configure the local Supabase MCP server to work with Trae AI.

## 🚀 Quick Setup

### Prerequisites

1. **Node.js 18+** installed
2. **Supabase CLI** installed globally
3. **<PERSON>er & Docker Compose** for local Supabase

### Step 1: Start Local Supabase

```bash
# Navigate to your project directory
cd /home/<USER>/local-supabase-mcp

# Start Supabase services
supabase start

# Get your connection details
supabase status
```

### Step 2: Build the MCP Server

The MCP server is already built and ready to use at:
```
/home/<USER>/local-supabase-mcp/packages/mcp-server-supabase/dist/transports/stdio.js
```

### Step 3: Configure Trae AI

Add this configuration to your Trae AI MCP settings:

```json
{
  "mcpServers": {
    "supabase-local": {
      "command": "node",
      "args": [
        "/home/<USER>/local-supabase-mcp/packages/mcp-server-supabase/dist/transports/stdio.js"
      ],
      "env": {
        "SUPABASE_URL": "http://localhost:54321",
        "SUPABASE_ANON_KEY": "your-anon-key-from-supabase-status",
        "SUPABASE_SERVICE_ROLE_KEY": "your-service-role-key-from-supabase-status",
        "READ_ONLY": "false",
        "DEBUG_SQL": "false"
      }
    }
  }
}
```

### Step 4: Update API Keys

1. Run `supabase status` to get your local keys
2. Replace the placeholder keys in the configuration:
   - `SUPABASE_ANON_KEY`: Copy the "anon key" from `supabase status`
   - `SUPABASE_SERVICE_ROLE_KEY`: Copy the "service_role key" from `supabase status`

### Step 5: Test the Connection

Once configured, you can test the MCP server by asking Trae AI to:
- List your database tables
- Query data from your Supabase instance
- Manage your database schema

## 🔧 Configuration Options

### Environment Variables

| Variable | Description | Default |
|----------|-------------|----------|
| `SUPABASE_URL` | Your local Supabase URL | `http://localhost:54321` |
| `SUPABASE_ANON_KEY` | Anonymous access key | From `supabase status` |
| `SUPABASE_SERVICE_ROLE_KEY` | Service role key | From `supabase status` |
| `READ_ONLY` | Restrict to read-only operations | `false` |
| `DEBUG_SQL` | Log SQL queries for debugging | `false` |

### Read-Only Mode

For safety, you can enable read-only mode by setting:
```json
"READ_ONLY": "true"
```

### Debug Mode

To see SQL queries in the logs, enable debug mode:
```json
"DEBUG_SQL": "true"
```

## 🛠️ Available Tools

Once configured, the MCP server provides these tools to Trae AI:

- **Database Operations**: Query, insert, update, delete data
- **Schema Management**: Create/modify tables, indexes, constraints
- **User Management**: Manage database users and permissions
- **Edge Functions**: Deploy and manage Supabase Edge Functions
- **Storage**: Manage file uploads and storage buckets
- **Real-time**: Configure real-time subscriptions

## 🔍 Troubleshooting

### Common Issues

1. **"Command not found" error**:
   - Ensure Node.js is installed and in your PATH
   - Verify the path to the stdio.js file is correct

2. **"Connection refused" error**:
   - Make sure Supabase is running: `supabase status`
   - Check if the URL and port are correct

3. **"Authentication failed" error**:
   - Verify your API keys are correct
   - Run `supabase status` to get fresh keys

### Getting Help

If you encounter issues:
1. Check the Supabase logs: `supabase logs`
2. Enable debug mode to see SQL queries
3. Verify your local Supabase is running properly

## 📁 Example Configuration File

A complete example configuration is available at:
```
/home/<USER>/local-supabase-mcp/packages/mcp-server-supabase/examples/trae-ai-config.json
```

This file contains the exact configuration needed for Trae AI with your current setup.