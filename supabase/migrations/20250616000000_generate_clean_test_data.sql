-- Clean Test Data Generation for Memory Master Schema
-- This migration creates comprehensive, valid test data with consistent app_id values

-- First, let's create some test users if they don't exist
DO $$
DECLARE
    test_user_1_id UUID;
    test_user_2_id UUID;
    test_user_3_id UUID;
    test_app_1_id UUID;
    test_app_2_id UUID;
    test_app_3_id UUID;
BEGIN
    -- Insert test users
    INSERT INTO memory_master.users (id, user_id, name, email, metadata, created_at, updated_at, email_verified, last_sign_in_at)
    VALUES 
      (gen_random_uuid(), 'test_user_1', '<PERSON>', '<EMAIL>', '{"role": "developer", "preferences": {"theme": "dark"}}', NOW(), NOW(), true, NOW()),
      (gen_random_uuid(), 'test_user_2', '<PERSON>', '<EMAIL>', '{"role": "admin", "preferences": {"theme": "light"}}', NOW(), NOW(), true, NOW()),
      (gen_random_uuid(), 'test_user_3', '<PERSON>', '<EMAIL>', '{"role": "user", "preferences": {"theme": "auto"}}', NOW(), NOW(), false, NOW() - INTERVAL '1 day')
    ON CONFLICT (user_id) DO NOTHING;

    -- Get user IDs
    SELECT id INTO test_user_1_id FROM memory_master.users WHERE user_id = 'test_user_1';
    SELECT id INTO test_user_2_id FROM memory_master.users WHERE user_id = 'test_user_2';
    SELECT id INTO test_user_3_id FROM memory_master.users WHERE user_id = 'test_user_3';

    -- Create test apps with consistent IDs
    INSERT INTO memory_master.apps (id, owner_id, name, description, metadata, is_active, created_at, updated_at)
    VALUES 
      (gen_random_uuid(), test_user_1_id, 'Memory Assistant Pro', 'Advanced memory management application', '{"version": "2.1.0", "environment": "test", "features": ["ai_search", "auto_categorize"]}', true, NOW(), NOW()),
      (gen_random_uuid(), test_user_2_id, 'Personal Knowledge Base', 'Personal information management system', '{"version": "1.5.2", "environment": "test", "features": ["tagging", "export"]}', true, NOW(), NOW()),
      (gen_random_uuid(), test_user_3_id, 'Team Collaboration Hub', 'Shared memory space for teams', '{"version": "3.0.0", "environment": "test", "features": ["sharing", "permissions"]}', false, NOW(), NOW())
    ON CONFLICT DO NOTHING;

    -- Get app IDs
    SELECT id INTO test_app_1_id FROM memory_master.apps WHERE name = 'Memory Assistant Pro';
    SELECT id INTO test_app_2_id FROM memory_master.apps WHERE name = 'Personal Knowledge Base';
    SELECT id INTO test_app_3_id FROM memory_master.apps WHERE name = 'Team Collaboration Hub';

    -- Create test categories
    INSERT INTO memory_master.categories (id, name, description, color, metadata, created_at, updated_at)
    VALUES 
      (gen_random_uuid(), 'Work', 'Work-related memories', '#FF6B6B', '{"priority": "high", "auto_archive_days": 365}', NOW(), NOW()),
      (gen_random_uuid(), 'Personal', 'Personal memories and notes', '#4ECDC4', '{"priority": "medium", "auto_archive_days": 180}', NOW(), NOW()),
      (gen_random_uuid(), 'Learning', 'Educational content and insights', '#45B7D1', '{"priority": "high", "auto_archive_days": 730}', NOW(), NOW()),
      (gen_random_uuid(), 'Ideas', 'Creative ideas and brainstorming', '#96CEB4', '{"priority": "medium", "auto_archive_days": 90}', NOW(), NOW()),
      (gen_random_uuid(), 'Tasks', 'Task-related information', '#FFEAA7', '{"priority": "high", "auto_archive_days": 30}', NOW(), NOW())
    ON CONFLICT (name) DO NOTHING;

    -- Create comprehensive test memories with valid JSON and consistent app_ids
    INSERT INTO memory_master.memories (id, user_id, app_id, content, vector, metadata, state, created_at, updated_at)
    SELECT 
      gen_random_uuid(),
      CASE 
        WHEN i % 3 = 0 THEN test_user_1_id
        WHEN i % 3 = 1 THEN test_user_2_id
        ELSE test_user_3_id
      END,
      CASE 
        WHEN i % 3 = 0 THEN test_app_1_id
        WHEN i % 3 = 1 THEN test_app_2_id
        ELSE test_app_3_id
      END,
      'Test memory content ' || i || ': ' || 
      CASE 
        WHEN i % 5 = 0 THEN 'Important project milestone reached with successful completion of phase ' || (i/5)
        WHEN i % 5 = 1 THEN 'Meeting notes from discussion about feature implementation and technical requirements'
        WHEN i % 5 = 2 THEN 'Research findings on best practices for database optimization and performance tuning'
        WHEN i % 5 = 3 THEN 'Personal reflection on learning progress and skill development in technology stack'
        ELSE 'Creative idea for improving user experience and interface design patterns'
      END,
      NULL, -- vector field
      jsonb_build_object(
        'tags', ARRAY['test', 'generated', 
          CASE WHEN i % 4 = 0 THEN 'important' 
               WHEN i % 4 = 1 THEN 'review' 
               WHEN i % 4 = 2 THEN 'draft'
               ELSE 'archived' END],
        'priority', CASE WHEN i % 3 = 0 THEN 'high' WHEN i % 3 = 1 THEN 'medium' ELSE 'low' END,
        'source', 'test_data_generator',
        'confidence_score', (random() * 100)::int,
        'word_count', (50 + random() * 200)::int,
        'language', 'en',
        'sentiment', CASE WHEN i % 4 = 0 THEN 'positive' WHEN i % 4 = 1 THEN 'neutral' WHEN i % 4 = 2 THEN 'negative' ELSE 'mixed' END
      ),
      CASE WHEN i % 6 = 0 THEN 'active' WHEN i % 6 = 1 THEN 'archived' ELSE 'active' END,
      NOW() - (random() * INTERVAL '30 days'),
      NOW() - (random() * INTERVAL '7 days')
    FROM generate_series(1, 50) AS i;

    -- Create memory access logs with consistent app_ids
    INSERT INTO memory_master.memory_access_logs (id, memory_id, app_id, accessed_at, access_type, metadata)
    SELECT
      gen_random_uuid(),
      m.id,
      m.app_id, -- Use the same app_id as the memory to ensure consistency
      NOW() - (random() * INTERVAL '7 days'),
      CASE
        WHEN random() < 0.4 THEN 'read'
        WHEN random() < 0.7 THEN 'search'
        WHEN random() < 0.9 THEN 'update'
        ELSE 'delete'
      END,
      jsonb_build_object(
        'user_agent', 'TestClient/1.0',
        'ip_address', '127.0.0.1',
        'session_id', 'test_session_' || (random() * 1000)::int,
        'duration_ms', (10 + random() * 500)::int,
        'success', CASE WHEN random() < 0.95 THEN true ELSE false END,
        'error_code', CASE WHEN random() < 0.05 THEN 'timeout' ELSE null END
      )
    FROM memory_master.memories m
    WHERE m.content LIKE 'Test memory content%'
    ORDER BY random()
    LIMIT 100;

    -- Create memory categories relationships
    INSERT INTO memory_master.memory_categories (id, memory_id, category_id, created_at)
    SELECT
      gen_random_uuid(),
      m.id,
      c.id,
      NOW() - (random() * INTERVAL '30 days')
    FROM memory_master.memories m
    CROSS JOIN memory_master.categories c
    WHERE m.content LIKE 'Test memory content%'
    AND random() < 0.3 -- Only 30% of memories get categorized
    ORDER BY random()
    LIMIT 75;

    -- Create memory status history
    INSERT INTO memory_master.memory_status_history (id, memory_id, old_status, new_status, changed_at, changed_by, metadata)
    SELECT
      gen_random_uuid(),
      m.id,
      'draft',
      'active',
      m.created_at + INTERVAL '1 hour',
      m.user_id,
      jsonb_build_object(
        'reason', 'automated_activation',
        'batch_id', 'test_batch_' || (random() * 100)::int
      )
    FROM memory_master.memories m
    WHERE m.content LIKE 'Test memory content%'
    AND m.state = 'active'
    ORDER BY random()
    LIMIT 30;

END $$;
