# MCP Client Configuration Examples

This directory contains configuration examples for various MCP clients to connect to your local Supabase MCP server.

## 🔧 Quick Setup

1. **Get your local Supabase keys**:
   ```bash
   supabase status
   ```

2. **Build the MCP server**:
   ```bash
   cd packages/mcp-server-supabase
   npm run build
   ```

3. **Choose your client configuration** from the examples below.

## 📋 Client Configurations

### Claude Desktop

**File**: [`claude-desktop-config.json`](./claude-desktop-config.json)

**Configuration location**:
- **macOS**: `~/Library/Application Support/Claude/claude_desktop_config.json`
- **Windows**: `%APPDATA%\Claude\claude_desktop_config.json`
- **Linux**: `~/.config/Claude/claude_desktop_config.json`

**Steps**:
1. Copy the configuration from `claude-desktop-config.json`
2. Update the `args` path to your actual project location
3. Replace the API keys with your values from `supabase status`
4. Restart Claude Desktop

### Cline (VS Code Extension)

**File**: [`cline-config.json`](./cline-config.json)

**Steps**:
1. Open VS Code
2. Install the Cline extension
3. Open Cline settings
4. Add the configuration from `cline-config.json`
5. Update the paths and API keys
6. Restart the extension

### Cursor IDE

**File**: [`cursor-config.json`](./cursor-config.json)

**Steps**:
1. Open Cursor IDE
2. Go to MCP settings
3. Add the configuration from `cursor-config.json`
4. Update the paths and API keys
5. Restart Cursor

## 🔑 Configuration Variables

Replace these placeholders in any configuration:

| Placeholder | Description | How to get |
|-------------|-------------|------------|
| `/absolute/path/to/packages/mcp-server-supabase/dist/transports/stdio.js` | Full path to the built MCP server | Use `pwd` in the project directory |
| `your-anon-key-from-supabase-status` | Anonymous API key | Copy from `supabase status` output |
| `your-service-role-key-from-supabase-status` | Service role API key | Copy from `supabase status` output |

## 🛠️ Customization Options

### Environment Variables

You can customize the behavior by adding these environment variables to the `env` section:

```json
{
  "env": {
    "SUPABASE_URL": "http://localhost:54321",
    "SUPABASE_ANON_KEY": "your-anon-key",
    "SUPABASE_SERVICE_ROLE_KEY": "your-service-role-key",
    "READ_ONLY": "true",           // Set to "true" for read-only mode
    "DEBUG_SQL": "true"            // Set to "true" to log SQL queries
  }
}
```

### Alternative Node.js Paths

If you have Node.js installed in a different location, update the `command` field:

- **Windows**: `"command": "C:\\Program Files\\nodejs\\node.exe"`
- **macOS (Homebrew)**: `"command": "/opt/homebrew/bin/node"`
- **Linux (nvm)**: `"command": "/home/<USER>/.nvm/versions/node/v18.0.0/bin/node"`

### Custom Supabase Ports

If you're using custom ports for your local Supabase instance, update the URLs:

```json
{
  "env": {
    "SUPABASE_URL": "http://localhost:YOUR_API_PORT",
    "DATABASE_URL": "postgresql://postgres:postgres@localhost:YOUR_DB_PORT/postgres"
  }
}
```

## 🚨 Troubleshooting

### Common Issues

1. **"Command not found" errors**:
   - Verify Node.js path: `which node` (macOS/Linux) or `where node` (Windows)
   - Use full path to node executable

2. **"Cannot find module" errors**:
   - Ensure you've run `npm run build` in the MCP server directory
   - Check that the path to `stdio.js` is correct

3. **Connection refused**:
   - Verify Supabase is running: `supabase status`
   - Check that ports aren't blocked by firewall
   - Ensure API keys are correct and current

4. **Permission denied**:
   - Make sure the MCP server files have execute permissions
   - On Unix systems: `chmod +x dist/transports/stdio.js`

### Debugging

Enable debug mode to see detailed logs:

```json
{
  "env": {
    "DEBUG_SQL": "true"
  }
}
```

### Testing Configuration

Test your configuration by running the MCP server manually:

```bash
cd packages/mcp-server-supabase
node dist/transports/stdio.js --help
```

## 📚 Additional Resources

- [Model Context Protocol Documentation](https://modelcontextprotocol.io/)
- [Supabase Local Development Guide](https://supabase.com/docs/guides/local-development)
- [Claude Desktop MCP Setup](https://docs.anthropic.com/claude/docs/model-context-protocol)

## 🤝 Need Help?

If you encounter issues:

1. Check the main [README.md](../README.md) troubleshooting section
2. Verify your local Supabase setup with `supabase status`
3. Test the MCP server directly with `npm run dev:local`
4. Check the [Supabase Discord](https://discord.supabase.com) community

## 🔄 Updating Configuration

When you restart your local Supabase instance, the API keys may change. To update:

1. Run `supabase status` to get new keys
2. Update your client configuration with new keys
3. Restart your MCP client

For automated key updates, consider using the setup script: `bash setup-local.sh`