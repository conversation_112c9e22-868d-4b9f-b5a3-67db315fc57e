# Supabase MCP Server - Local Self-Hosted Edition

A comprehensive MCP (Model Context Protocol) server for local self-hosted Supabase instances, providing seamless database operations, development tools, and edge function management for your local Supabase environment.

## 🚀 Quick Start

### Prerequisites

Before getting started, ensure you have the following installed:

1. **Docker & Docker Compose** - Required for running Supabase locally
   ```bash
   # Verify Docker installation
   docker --version
   docker compose version
   ```

2. **Supabase CLI** - For managing your local instance
   ```bash
   # Install globally via npm
   npm install -g supabase
   
   # Or download from GitHub releases
   # https://github.com/supabase/cli/releases
   ```

3. **Node.js 18+** - For running the MCP server
   ```bash
   # Verify Node.js version
   node --version  # Should be 18.0.0 or higher
   ```

### Step 1: Set Up Local Supabase

```bash
# Create or navigate to your project directory
mkdir my-supabase-project && cd my-supabase-project

# Initialize Supabase in your project
supabase init

# Start all Supabase services (this may take a few minutes on first run)
supabase start

# Verify everything is running and get connection details
supabase status
```

### Step 2: Install & Configure MCP Server

```bash
# Clone this repository or install via npm
git clone https://github.com/supabase-community/supabase-mcp.git
cd supabase-mcp/packages/mcp-server-supabase

# Install dependencies
npm install

# Copy environment template
cp .env.example .env

# Edit .env with your local Supabase details from 'supabase status'
```

### Step 3: Configure Environment Variables

Edit your `.env` file with the values from `supabase status`:

```bash
# Your .env file should look like this:
SUPABASE_URL=http://localhost:54321
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

### Step 4: Build & Test

```bash
# Build the project
npm run build

# Test the connection
npm run dev:local
```

## 🐳 Running in Docker

This MCP server can be run in a Docker container, which is useful for isolating the server and connecting it to a remote Supabase instance.

### Building the Docker Image

First, build the Docker image from the `packages/mcp-server-supabase` directory:

```bash
docker build -t supabase/mcp-server-supabase .
```

### Running with `docker run`

To run the container, you need to pass the Supabase connection details as environment variables.

```bash
docker run -it --rm \
  -e SUPABASE_URL=YOUR_REMOTE_SUPABASE_URL \
  -e SUPABASE_ANON_KEY=YOUR_REMOTE_SUPABASE_ANON_KEY \
  -e SUPABASE_SERVICE_ROLE_KEY=YOUR_REMOTE_SUPABASE_SERVICE_ROLE_KEY \
  --name mcp-server-supabase \
  supabase/mcp-server-supabase
```

### Running with `docker-compose`

For a more streamlined approach, you can use the provided `docker-compose.yml` file.

1.  **Copy and configure `.env`**:
    Create a `.env` file in `packages/mcp-server-supabase` and add your remote Supabase credentials:
    ```env
    SUPABASE_URL=YOUR_REMOTE_SUPABASE_URL
    SUPABASE_ANON_KEY=YOUR_REMOTE_SUPABASE_ANON_KEY
    SUPABASE_SERVICE_ROLE_KEY=YOUR_REMOTE_SUPABASE_SERVICE_ROLE_KEY
    ```

2.  **Start the container**:
    ```bash
    docker-compose up
    ```

    To run in detached mode, use `docker-compose up -d`.

##  MCP Client Configuration

This section provides configuration examples for various MCP clients to connect to the Supabase MCP server.

### Roo Code

#### Connecting to the Docker Container

To connect Roo Code to the MCP server running inside the Docker container, you can use the `docker exec` command. This allows you to run the server process within the container's context and attach to its standard I/O.

**Example Roo Code `settings.json` configuration:**

```json
"mcp.servers": [
  {
    "name": "supabase-docker",
    "command": "docker exec -i mcp-server-supabase node /usr/src/app/packages/mcp-server-supabase/dist/transports/stdio.js"
  }
]
```

This configuration tells Roo Code to:
1.  Use `docker exec -i` to execute a command inside the `mcp-server-supabase` container.
2.  The `-i` flag is crucial as it keeps `STDIN` open, allowing the client to communicate with the server.
3.  Run the server's entrypoint script directly using `node`.

The environment variables are already set in the `docker-compose.yml` file, so you don't need to provide them in the Roo Code configuration.

#### Connecting for Local Development (without Docker)

If you are running the server locally without Docker, you can use the following configuration:

**Example Roo Code `settings.json` configuration:**

```json
"mcp.servers": [
  {
    "name": "supabase-local",
    "command": "npm --workspace @supabase/mcp-server-supabase run dev:local",
    "env": {
      "SUPABASE_URL": "http://127.0.0.1:54321",
      "SUPABASE_ANON_KEY": "your-anon-key",
      "SUPABASE_SERVICE_ROLE_KEY": "your-service-role-key"
    }
  }
]
```

Replace the placeholder keys with the actual keys from your local Supabase instance.

### Other Clients (Claude Desktop, Cline, Cursor)

#### Local Node Process

For clients like Claude Desktop, Cline, or Cursor, you can run the server as a local Node.js process.

```json
{
  "mcpServers": {
    "supabase-local": {
      "command": "node",
      "args": [
        "/absolute/path/to/packages/mcp-server-supabase/dist/transports/stdio.js"
      ],
      "env": {
        "SUPABASE_URL": "http://localhost:54321",
        "SUPABASE_ANON_KEY": "your-anon-key-from-supabase-status",
        "SUPABASE_SERVICE_ROLE_KEY": "your-service-role-key-from-supabase-status"
      }
    }
  }
}
```

#### Docker Container

To connect these clients to the server running inside a Docker container, use this configuration:

```json
{
  "mcpServers": {
    "supabase-docker": {
      "command": "docker",
      "args": [
        "run",
        "-i",
        "--rm",
        "-e",
        "SUPABASE_URL=YOUR_REMOTE_SUPABASE_URL",
        "-e",
        "SUPABASE_ANON_KEY=YOUR_REMOTE_SUPABASE_ANON_KEY",
        "-e",
        "SUPABASE_SERVICE_ROLE_KEY=YOUR_REMOTE_SUPABASE_SERVICE_ROLE_KEY",
        "supabase/mcp-server-supabase"
      ]
    }
  }
}
```

## 🛠️ Available Tools

### Database Operations
- **`list_tables`** - List all tables in your local database with detailed information
- **`list_extensions`** - List installed PostgreSQL extensions and their versions
- **`execute_sql`** - Execute SQL queries on your local database with safety checks
- **`apply_migration`** - Apply database migrations to your local instance
- **`list_migrations`** - List all applied migrations with timestamps

### Local Development Tools
- **`check_local_connection`** - Verify connection to local Supabase with troubleshooting
- **`get_local_config`** - Display current configuration, URLs, and API keys
- **`supabase_status`** - Instructions for checking service status and health
- **`list_docker_services`** - List running Supabase Docker containers
- **`create_migration`** - Instructions for creating new database migrations
- **`reset_database`** - Instructions for safely resetting local database

### Data Inspection & Analysis
- **`list_auth_users`** - List users in the local authentication system
- **`list_storage_buckets`** - List storage buckets and their configurations
- **`inspect_table_schema`** - Get detailed schema information for specific tables
- **`explain_query`** - Analyze query performance with PostgreSQL EXPLAIN

### Development Utilities
- **`get_project_url`** - Get the local Supabase API URL
- **`get_anon_key`** - Retrieve the anonymous API key
- **`generate_typescript_types`** - Instructions for generating TypeScript types from schema

### Edge Functions
- **`list_edge_functions`** - List deployed edge functions in your local environment
- **`get_edge_function`** - Get details and metadata for specific edge functions

### Documentation & Help
- Various tools for querying Supabase documentation and getting contextual help

## ⚙️ Environment Variables

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `SUPABASE_URL` | Local Supabase instance URL | `http://localhost:54321` | ✅ |
| `SUPABASE_ANON_KEY` | Anonymous API key from `supabase status` | - | ✅ |
| `SUPABASE_SERVICE_ROLE_KEY` | Service role API key from `supabase status` | - | ✅ |
| `DATABASE_URL` | Direct PostgreSQL connection string | `postgresql://postgres:postgres@localhost:54322/postgres` | ❌ |
| `READ_ONLY` | Execute all queries in read-only mode | `false` | ❌ |
| `DEBUG_SQL` | Log SQL queries and operations to console | `false` | ❌ |

## 🖥️ CLI Usage

You can run the MCP server directly with CLI arguments:

```bash
# Using environment variables
npm run dev:local

# Using CLI arguments (after building)
node dist/transports/stdio.js \
  --supabase-url http://localhost:54321 \
  --anon-key your-anon-key \
  --service-key your-service-role-key \
  --read-only

# Build for production
npm run build

# Run built version
./dist/transports/stdio.js
```

## 🔄 Migration from Cloud Version

### Key Differences from Cloud Supabase MCP Server

#### ❌ Removed Features (Not Available Locally)
- **Project Management**: Create, pause, restore projects
- **Organization Management**: Organization-level operations
- **Branching Operations**: Database branching and merging
- **Cloud Logging**: Cloud-specific logging and monitoring
- **Cost Management**: Usage tracking and billing operations

#### ✅ Enhanced Local Features
- **Direct Database Access**: Direct PostgreSQL connections without API intermediaries
- **Docker Integration**: Container management and monitoring tools
- **Local Development Workflow**: Migration management, database reset, local testing
- **Service Discovery**: Automatic detection of local service configurations

#### 🔧 Modified Behavior
- **Authentication**: Uses API keys instead of personal access tokens
- **Service URLs**: Automatically configured for localhost ports
- **Error Handling**: Local-specific error messages and troubleshooting guides

### Migration Path

If you're migrating from the cloud version:

1. **Keep your existing cloud configuration** for production use
2. **Set up this local version** for development
3. **Use environment variables** to switch between configurations
4. **Update your MCP client configuration** to point to the local server

## 🚨 Troubleshooting

### Common Issues & Solutions

#### 1. Connection Refused Errors

```bash
# Check if Supabase is running
supabase status

# If services are not running
supabase start

# If start fails, try stopping and starting again
supabase stop
supabase start
```

#### 2. Invalid API Keys

```bash
# Get current keys from your running instance
supabase status

# Copy the anon key and service_role key to your .env file
# Keys change each time you run 'supabase start'
```

#### 3. Port Conflicts

```bash
# Check what's using Supabase ports
# Windows:
netstat -an | findstr "54321"
netstat -an | findstr "54322"
netstat -an | findstr "54323"

# Linux/macOS:
lsof -i :54321
lsof -i :54322
lsof -i :54323

# Stop conflicting services or configure different ports in config.toml
```

#### 4. Docker Issues

```bash
# Verify Docker is running
docker ps

# Check Docker Compose status
docker compose ps

# Restart all services
supabase stop
docker system prune -f
supabase start
```

#### 5. Permission Errors

```bash
# On Linux/macOS, ensure proper permissions
sudo chown -R $USER:$USER .

# Ensure Docker has proper permissions
sudo usermod -aG docker $USER
# (requires logout/login to take effect)
```

#### 6. Database Connection Issues

```bash
# Test direct database connection
psql "postgresql://postgres:postgres@localhost:54322/postgres"

# Check database logs
docker compose logs supabase-db

# Reset database if corrupted
supabase db reset
```

### Getting Help

1. **Connection Testing**: Use the `check_local_connection` MCP tool
2. **Configuration Verification**: Use the `get_local_config` MCP tool
3. **Service Status**: Run `supabase status` in terminal
4. **View Logs**: 
   - Supabase logs: `supabase logs`
   - Container logs: `docker compose logs [service-name]`
5. **Community Support**: Visit [Supabase Discord](https://discord.supabase.com)

### Debug Mode

Enable debug mode for detailed troubleshooting:

```bash
# Set in your .env file
DEBUG_SQL=true

# Or as environment variable
DEBUG_SQL=true npm run dev:local
```

## 🏗️ Development

### Building from Source

```bash
# Clone the repository
git clone https://github.com/supabase-community/supabase-mcp.git
cd supabase-mcp/packages/mcp-server-supabase

# Install dependencies
npm install

# Build the project
npm run build

# Run in development mode
npm run dev:local
```

### Testing

The project includes a comprehensive testing framework built on Vitest with support for unit, integration, and end-to-end testing.

#### Quick Start

```bash
# Run all tests
npm test

# Run specific test suites
npm run test:unit          # Fast unit tests (18 tests)
npm run test:integration   # Database integration tests
npm run test:e2e          # End-to-end workflow tests

# Run with coverage reporting
npm run test:coverage

# Run tests in watch mode during development
npm run test:watch
```

#### Test Types & Status

| Test Type | Command | Status | Description |
|-----------|---------|--------|-------------|
| **Unit Tests** | `npm run test:unit` | ✅ 18/18 passing | Test individual functions and components |
| **Integration Tests** | `npm run test:integration` | ✅ 7/7 passing | Test framework validation and database connectivity |
| **E2E Tests** | `npm run test:e2e` | 🔧 In development | Test complete user workflows |

#### Running Specific Tests

```bash
# Run framework validation tests (recommended first test)
npx vitest run test/framework-validation.integration.ts --project integration

# Run database integration tests (requires schema permissions)
npx vitest run test/local-database.integration.ts --project integration

# Run with verbose output for debugging
npx vitest run --reporter=verbose

# Run single test file
npx vitest run src/utils.test.ts
```

#### Test Environment Setup

Tests require a `.env.local` file with your local Supabase configuration:

```env
# Required for all tests
SUPABASE_URL=https://devdb.syncrobit.net
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

# Optional
DATABASE_URL=postgresql://postgres:postgres@localhost:54322/postgres
READ_ONLY=false
DEBUG_SQL=false
```

#### Documentation

- 📚 [Complete Testing Guide](./docs/TESTING.md) - Comprehensive testing documentation
- ⚡ [Quick Reference](./docs/TESTING_QUICK_REFERENCE.md) - Commands and patterns cheat sheet
- 📁 [Test Directory Guide](./test/README.md) - Overview of test files and structure

#### Coverage Reports

```bash
# Generate coverage report
npm run test:coverage

# View HTML coverage report
open coverage/index.html
```

Current coverage targets:
- **Branches**: 80%
- **Functions**: 80%
- **Lines**: 80%
- **Statements**: 80%

### Project Structure

```
packages/mcp-server-supabase/
├── src/
│   ├── config/              # Configuration management
│   ├── platform/            # Platform implementations
│   ├── tools/               # MCP tool definitions
│   ├── transports/          # Transport layers (stdio, etc.)
│   ├── server.ts            # Main server implementation
│   └── index.ts             # Entry point
├── examples/                # Client configuration examples
├── .env.example             # Environment template
└── setup-local.sh           # Automated setup script
```

## 🤝 Contributing

We welcome contributions! This local-only version maintains the same architecture as the original cloud-based Supabase MCP server but focuses exclusively on local self-hosted instances.

### Development Guidelines

1. **Follow TypeScript best practices**
2. **Add tests for new features**
3. **Update documentation**
4. **Test with real local Supabase instances**

### Submitting Changes

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

Apache-2.0

## 🔗 Related Projects

- [Supabase](https://supabase.com) - The open source Firebase alternative
- [Model Context Protocol](https://modelcontextprotocol.io) - Protocol for AI model context
- [Claude Desktop](https://claude.ai) - AI assistant with MCP support

---

**Need help?** Check out our [troubleshooting guide](#-troubleshooting) or join the [Supabase community](https://discord.supabase.com).