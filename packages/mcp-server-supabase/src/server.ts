import { createMcpServer, type Tool } from '@supabase/mcp-utils';
import packageJson from '../package.json' with { type: 'json' };
import { createContentApiClient } from './content-api/index.js';
import { getLocalConfig, type LocalConfig } from './config/local-config.js';
import { createLocalSupabasePlatform } from './platform/local-platform.js';
import type { SupabasePlatform } from './platform/types.js';
import { getDatabaseOperationTools } from './tools/database-operation-tools.js';
import { getDevelopmentTools } from './tools/development-tools.js';
import { getDocsTools } from './tools/docs-tools.js';
import { getEdgeFunctionTools } from './tools/edge-function-tools.js';
import { getLocalDevelopmentTools } from './tools/local-development-tools.js';

const { version } = packageJson;

export type LocalSupabaseMcpServerOptions = {
  /**
   * Local Supabase configuration.
   */
  config?: Partial<LocalConfig>;

  /**
   * The API URL for the Supabase Content API.
   */
  contentApiUrl?: string;

  /**
   * Executes database queries in read-only mode if true.
   */
  readOnly?: boolean;
};

/**
 * Creates an MCP server for interacting with local Supabase instances.
 */
export function createLocalSupabaseMcpServer(options: LocalSupabaseMcpServerOptions = {}) {
  const {
    config,
    readOnly,
    contentApiUrl = 'https://supabase.com/docs/api/graphql',
  } = options;

  // Get configuration from environment or provided config
  const localConfig = config ? { ...getLocalConfig(), ...config } : getLocalConfig();
  
  // Create the local platform
  const platform = createLocalSupabasePlatform(localConfig);
  
  const contentApiClientPromise = createContentApiClient(contentApiUrl);

  const server = createMcpServer({
    name: 'supabase-local',
    version,
    async onInitialize(info: any) {
      // Initialize the local platform
      await platform.init?.(info);
    },
    tools: async () => {
      const contentApiClient = await contentApiClientPromise;
      const tools: Record<string, Tool> = {};

      // For local instances, we always use a single "local" project ID
      const projectId = 'local';

      // Add local-relevant tools only
      Object.assign(
        tools,
        getDatabaseOperationTools({ platform, projectId, readOnly }),
        getEdgeFunctionTools({ platform, projectId }),
        getDevelopmentTools({ platform, projectId }),
        getLocalDevelopmentTools({ platform, projectId }),
        getDocsTools({ contentApiClient })
      );

      return tools;
    },
  });

  return server;
}

// Keep the old function name for backward compatibility, but it now creates a local server
export const createSupabaseMcpServer = createLocalSupabaseMcpServer;
