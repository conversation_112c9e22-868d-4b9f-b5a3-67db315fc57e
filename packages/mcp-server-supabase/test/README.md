# Test Directory

This directory contains integration and end-to-end tests for the MCP Server Supabase project.

## 📁 Directory Structure

```
test/
├── README.md                              # This file
├── framework-validation.integration.ts    # Testing framework validation
├── local-database.integration.ts         # Database integration tests
└── stdio.integration.ts                  # STDIO transport integration tests
```

## 🧪 Test Files

### `framework-validation.integration.ts`
**Purpose**: Validates that the testing framework is working correctly
**What it tests**:
- Supabase client connection
- Environment variable loading
- Vitest framework functionality
- Async operation handling
- Error handling
- JSON processing
- Performance measurement

**Status**: ✅ All tests passing (7/7)

### `local-database.integration.ts`
**Purpose**: Tests direct database access and schema validation
**What it tests**:
- Database connectivity with service role
- Memory master schema access
- JSON field validation
- Referential integrity
- Query performance

**Status**: ⚠️ Requires memory_master schema permissions

### `stdio.integration.ts`
**Purpose**: Tests the MCP server STDIO transport
**What it tests**:
- Server startup and connection
- Tool listing functionality
- Authentication handling
- Error scenarios

**Status**: 🔧 Needs environment configuration updates

## 🚀 Running Tests

### Run All Integration Tests
```bash
npm run test:integration
```

### Run Specific Test File
```bash
# Framework validation (recommended first test)
npx vitest run test/framework-validation.integration.ts --project integration

# Database tests (requires schema permissions)
npx vitest run test/local-database.integration.ts --project integration

# STDIO transport tests
npx vitest run test/stdio.integration.ts --project integration
```

### Run with Verbose Output
```bash
npx vitest run test/ --project integration --reporter=verbose
```

## 🔧 Configuration

### Environment Variables
Tests require these environment variables in `.env.local`:

```env
# Required for all tests
SUPABASE_URL=https://devdb.syncrobit.net
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

# Required for database tests
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

# Optional
DATABASE_URL=postgresql://postgres:postgres@localhost:54322/postgres
READ_ONLY=false
DEBUG_SQL=false
```

### Prerequisites
- Local Supabase instance running
- Memory master schema exposed (for database tests)
- Service role permissions configured

## 📊 Test Status Overview

| Test File | Status | Tests | Notes |
|-----------|--------|-------|-------|
| `framework-validation.integration.ts` | ✅ Passing | 7/7 | Framework validation complete |
| `local-database.integration.ts` | ⚠️ Blocked | 0/5 | Needs schema permissions |
| `stdio.integration.ts` | 🔧 Needs Fix | 0/2 | Environment configuration |

## 🎯 Adding New Tests

### Integration Test Template
```typescript
import { describe, expect, test } from 'vitest';

describe('New Integration Test Suite', () => {
  test('should test specific integration', async () => {
    // Arrange
    const setup = await setupTestEnvironment();
    
    // Act
    const result = await performOperation(setup);
    
    // Assert
    expect(result).toBeDefined();
    expect(result.success).toBe(true);
  });
});
```

### Naming Convention
- Use descriptive names: `feature-name.integration.ts`
- Group related tests in the same file
- Use clear test descriptions

### Best Practices
1. **Setup/Teardown**: Clean up resources after tests
2. **Isolation**: Tests should not depend on each other
3. **Real Services**: Use actual Supabase instance for integration tests
4. **Error Cases**: Test both success and failure scenarios
5. **Performance**: Include performance assertions where relevant

## 🚨 Troubleshooting

### Common Issues

#### Tests Fail with "Permission Denied"
- Ensure service role key is correct
- Verify schema is exposed through API
- Check RLS policies

#### Environment Variables Undefined
- Verify `.env.local` exists in package root
- Check variable names match exactly
- Ensure no typos in values

#### Connection Timeouts
- Verify Supabase instance is running
- Check network connectivity
- Increase test timeout if needed

#### Schema Not Found
- Confirm memory_master schema is exposed
- Verify API configuration
- Check schema permissions

### Debug Commands
```bash
# Run with debug output
DEBUG=* npx vitest run test/

# Single test with verbose logging
npx vitest run test/framework-validation.integration.ts --reporter=verbose

# Check environment loading
node -e "console.log(process.env.SUPABASE_URL)"
```

## 📈 Coverage

Integration tests contribute to overall coverage metrics:

```bash
# Run with coverage
npx vitest run test/ --coverage

# View coverage report
open coverage/index.html
```

## 🔄 Continuous Integration

These tests run in CI/CD pipeline:
- Framework validation: Always runs
- Database tests: Run when schema is properly configured
- STDIO tests: Run after environment fixes

## 📚 Related Documentation

- [Main Testing Documentation](../docs/TESTING.md)
- [Quick Reference Guide](../docs/TESTING_QUICK_REFERENCE.md)
- [Vitest Documentation](https://vitest.dev/)

---

**Need Help?** Check the main testing documentation or create an issue for test-specific problems.
